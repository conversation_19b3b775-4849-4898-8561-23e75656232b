# Razorpay Live API Requirements - AutoMaxLib

## 📋 **Checklist for Razorpay Live API Approval**

### ✅ **Website Requirements (COMPLETED)**

1. **Legal Pages** ✅

   - [x] Privacy Policy - `/privacy-policy`
   - [x] Terms of Service - `/terms-of-service`
   - [x] Refund & Cancellation Policy - `/refund-policy`
   - [x] Contact Us - `/contact`
   - [x] About Us - `/about`

2. **Website Content** ✅

   - [x] Professional design and layout
   - [x] Clear product/service description
   - [x] Pricing information
   - [x] Contact information
   - [x] Business address (needs to be added)
   - [x] Working contact form

3. **Footer Links** ✅
   - [x] All legal pages linked in footer
   - [x] Contact information accessible
   - [x] Professional appearance

### 🔄 **Business Information Required**

You need to update the following placeholders in your legal pages:

#### **Contact Information**

```
Email: <EMAIL>
Phone: +91 **********
Address: Nimbhora MIDC Road
         Amravati, Maharashtra
         India
```

#### **Business Details**

- **Business Name**: AutoMaxLib
- **Business Type**: SaaS Platform / Software Service
- **Industry**: Developer Tools & Automation
- **Website**: https://automaxlib.com (or your domain)

### 📝 **Razorpay Account Setup Steps**

#### **Step 1: Complete KYC Documentation**

1. **Business Registration**

   - GST Certificate (if applicable)
   - Business PAN Card
   - Certificate of Incorporation (if company)
   - Partnership Deed (if partnership)

2. **Personal KYC**
   - Aadhaar Card
   - PAN Card
   - Bank Account Details
   - Cancelled Cheque

#### **Step 2: Website Verification**

1. **Submit Website URL**: https://autogitpilot.com
2. **Verify Legal Pages**: Razorpay will check all legal pages
3. **Business Model Verification**: Explain SaaS subscription model

#### **Step 3: Enable International Payments**

1. **Go to**: Razorpay Dashboard → Settings → International
2. **Enable**: International Payments
3. **Select Currencies**: USD, EUR, GBP (as needed)
4. **Choose Methods**: International Cards, PayPal

### 🏢 **Business Information Template**

Fill out this information for Razorpay application:

```
Business Name: AutoMaxLib
Business Type: Private Limited Company / Sole Proprietorship
Industry: Software as a Service (SaaS)
Business Model: Subscription-based developer tools

Product/Service Description:
GitHub automation platform that helps developers maintain consistent
contribution graphs through intelligent commit scheduling, AI-powered
content generation, and repository management tools.

Target Market: Software developers, students, professionals
Pricing Model: Freemium (Free trial + Premium subscription)
Monthly Volume: [Estimated transaction volume]
Average Transaction: ₹500 (monthly) / ₹5000 (yearly)

Website: https://automaxlib.com
Support Email: <EMAIL>
Business Address: Nimbhora MIDC Road, Amravati, Maharashtra, India
```

### 💳 **Payment Integration Details**

#### **Current Setup**

- **Test Environment**: ✅ Working with INR
- **Payment Methods**: Cards, UPI, Net Banking, Wallets
- **Security**: PCI DSS compliant through Razorpay
- **Webhooks**: ✅ Configured for payment verification

#### **Live Environment Requirements**

- **SSL Certificate**: Required for live payments
- **Domain Verification**: Business domain ownership
- **Webhook URLs**: Must be HTTPS in production

### 🔐 **Security & Compliance**

#### **Data Protection** ✅

- User data encryption
- Secure authentication (Clerk)
- GitHub OAuth integration
- Payment data handled by Razorpay (PCI compliant)

#### **Legal Compliance** ✅

- Privacy Policy covers data collection and usage
- Terms of Service define user responsibilities
- Refund Policy meets consumer protection requirements
- Contact information for dispute resolution

### 📞 **Next Steps for Live API Approval**

1. **Update Business Information**

   - Add real contact details to legal pages
   - Update placeholders with actual business address
   - Ensure phone number is reachable

2. **Submit Razorpay Application**

   - Login to Razorpay Dashboard
   - Go to Settings → Account & Settings
   - Submit live API key request
   - Upload required KYC documents

3. **Website Review**

   - Razorpay will review your website
   - Verify all legal pages are accessible
   - Check business legitimacy and compliance

4. **Approval Timeline**
   - Initial review: 2-3 business days
   - Document verification: 3-5 business days
   - Final approval: 1-2 business days
   - Total: 7-10 business days typically

### ⚠️ **Important Notes**

1. **Domain Requirements**

   - Use a professional domain (not localhost)
   - Ensure SSL certificate is installed
   - All legal pages must be publicly accessible

2. **Business Legitimacy**

   - Provide genuine business information
   - Ensure contact details are working
   - Have proper business registration documents

3. **Compliance**
   - All legal pages must be comprehensive
   - Contact information must be accurate
   - Refund policy must be clearly stated

### 📧 **Contact for Support**

If you need help with the approval process:

- **Razorpay Support**: <EMAIL>
- **Phone**: 1800-120-020-020
- **Business Hours**: Mon-Fri, 9 AM - 6 PM IST

### 🎯 **Success Criteria**

Your website is now ready for Razorpay live API approval with:

- ✅ All required legal pages
- ✅ Professional website design
- ✅ Clear business model
- ✅ Proper contact information structure
- ✅ Comprehensive policies
- ✅ Working payment integration (test mode)

**Next Action**: Update any remaining placeholder information with your actual business details and submit the live API request to Razorpay.
