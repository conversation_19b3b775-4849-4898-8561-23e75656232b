{"name": "automaxlib-backend", "version": "1.0.0", "description": "Backend API for AutoMaxLib", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "pm2:start": "pm2 start ecosystem.config.js", "pm2:start:prod": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop autogitpilot-backend", "pm2:restart": "pm2 restart autogitpilot-backend", "pm2:reload": "pm2 reload autogitpilot-backend", "pm2:delete": "pm2 delete autogitpilot-backend", "pm2:logs": "pm2 logs autogitpilot-backend", "pm2:monit": "pm2 monit", "pm2:status": "pm2 status", "pm2:flush": "pm2 flush", "production": "NODE_ENV=production pm2 start ecosystem.config.js --env production", "staging": "NODE_ENV=staging pm2 start ecosystem.config.js --env staging"}, "dependencies": {"@clerk/clerk-sdk-node": "^4.13.15", "@google/generative-ai": "^0.2.1", "axios": "^1.6.2", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "express-mongo-sanitize": "^2.2.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "openai": "^4.20.1", "razorpay": "^2.9.2", "socket.io": "^4.8.1", "xss": "^1.0.14", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ioredis": "^5.6.1", "redis": "^5.6.1", "node-cache": "^5.1.2", "pm2": "^6.0.8", "svix": "^1.15.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["github", "automation", "commits", "api"], "author": "AutoMaxLib", "license": "MIT"}