Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCAF5B0000 ntdll.dll
7FFCAE690000 KERNEL32.DLL
7FFCAC6A0000 KERNELBASE.dll
7FFCAE760000 USER32.dll
7FFCAD210000 win32u.dll
7FFCAD330000 GDI32.dll
000210040000 msys-2.0.dll
7FFCACE70000 gdi32full.dll
7FFCAD170000 msvcp_win.dll
7FFCAD050000 ucrtbase.dll
7FFCADF40000 advapi32.dll
7FFCAF120000 msvcrt.dll
7FFCAEAF0000 sechost.dll
7FFCAD020000 bcrypt.dll
7FFCAEC40000 RPCRT4.dll
7FFCABE10000 CRYPTBASE.DLL
7FFCACFA0000 bcryptPrimitives.dll
7FFCAED60000 IMM32.DLL
