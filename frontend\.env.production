# ===========================================
# AutoGitPilot Frontend Production Environment
# ===========================================

# Authentication - Clerk (Production Keys)
# Replace with your actual Clerk production publishable key from dashboard
VITE_CLERK_PUBLISHABLE_KEY=pk_live_REPLACE_WITH_YOUR_ACTUAL_CLERK_PRODUCTION_KEY

# API Configuration
VITE_API_BASE_URL=https://automaxlib.onrender.com/api
VITE_API_TIMEOUT=30000

# Payment Processing - Razorpay (Production Keys)
VITE_RAZORPAY_KEY_ID=rzp_live_your_production_key

# Application Configuration
VITE_APP_NAME=AutoGitPilot
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Automate your GitHub commits and maintain your contribution streak

# Feature Flags
VITE_ENABLE_PAYMENTS=true
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG=false

# External Services (Production)
VITE_SENTRY_DSN=https://<EMAIL>/project_id
VITE_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
VITE_HOTJAR_ID=

# Social Media & SEO
VITE_SOCIAL_TWITTER=@automaxlib
VITE_SOCIAL_GITHUB=https://github.com/Aniruddha434/AutoMaxLib
VITE_SOCIAL_DISCORD=
VITE_CANONICAL_URL=https://www.automaxlib.online

# Performance & Caching
VITE_ENABLE_SW=true
VITE_CACHE_VERSION=1.0.0
