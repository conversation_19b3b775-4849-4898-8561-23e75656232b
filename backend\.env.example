# ===========================================
# AutoGitPilot Backend Environment Configuration
# ===========================================

# Server Configuration
PORT=5000
NODE_ENV=development
HOST=0.0.0.0

# Security
JWT_SECRET=your_jwt_secret_here_minimum_32_characters_long
SESSION_SECRET=your_session_secret_here_minimum_32_characters_long
ENCRYPTION_KEY=your_encryption_key_here_32_characters_exactly

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/autogitpilot
MONGODB_OPTIONS=retryWrites=true&w=majority&maxPoolSize=10&serverSelectionTimeoutMS=5000&socketTimeoutMS=45000

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Authentication - Clerk
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key_here
CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Payment Processing - Razorpay (Optional - leave empty to disable)
RAZORPAY_KEY_ID=
RAZORPAY_KEY_SECRET=
RAZORPAY_WEBHOOK_SECRET=

# AI Services
OPENAI_API_KEY=sk-your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=AutoGitPilot

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# GitHub Integration
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_WEBHOOK_SECRET=your_github_webhook_secret

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# Monitoring & Health Checks
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
SENTRY_DSN=

# Production Optimizations
COMPRESSION_ENABLED=true
TRUST_PROXY=false
CORS_CREDENTIALS=true

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
