version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: autogitpilot-mongodb-prod
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGODB_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: autogitpilot
    volumes:
      - mongodb_data_prod:/data/db
      - mongodb_config_prod:/data/configdb
      - ./backend/scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
      - ./backups:/backups
    networks:
      - autogitpilot-network-prod
    command: mongod --auth --bind_ip_all
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: autogitpilot-redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    volumes:
      - redis_data_prod:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - autogitpilot-network-prod
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: autogitpilot-backend-prod
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: mongodb://${MONGODB_ROOT_USERNAME:-admin}:${MONGODB_ROOT_PASSWORD:-password}@mongodb:27017/autogitpilot?authSource=admin
      REDIS_URL: redis://redis:6379
    env_file:
      - ./backend/.env.production
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
      - ./backups:/app/backups
    networks:
      - autogitpilot-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Frontend (Nginx)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    container_name: autogitpilot-frontend-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      VITE_API_BASE_URL: ${FRONTEND_API_URL:-http://localhost:5000/api}
    env_file:
      - ./frontend/.env.production
    depends_on:
      backend:
        condition: service_healthy
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    networks:
      - autogitpilot-network-prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Nginx Reverse Proxy (Optional - for SSL termination)
  nginx-proxy:
    image: nginx:alpine
    container_name: autogitpilot-nginx-proxy-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/proxy.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - autogitpilot-network-prod
    profiles:
      - proxy

  # Monitoring (Prometheus - Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: autogitpilot-prometheus-prod
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data_prod:/prometheus
    networks:
      - autogitpilot-network-prod
    profiles:
      - monitoring

  # Grafana (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: autogitpilot-grafana-prod
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data_prod:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - autogitpilot-network-prod
    profiles:
      - monitoring

  # Log aggregation (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: autogitpilot-elasticsearch-prod
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data_prod:/usr/share/elasticsearch/data
    networks:
      - autogitpilot-network-prod
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: autogitpilot-logstash-prod
    restart: unless-stopped
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ./backend/logs:/logs:ro
    depends_on:
      - elasticsearch
    networks:
      - autogitpilot-network-prod
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: autogitpilot-kibana-prod
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - autogitpilot-network-prod
    profiles:
      - logging

  # Backup service
  backup:
    image: alpine:latest
    container_name: autogitpilot-backup-prod
    restart: "no"
    volumes:
      - mongodb_data_prod:/data/mongodb:ro
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    networks:
      - autogitpilot-network-prod
    command: /bin/sh -c "chmod +x /backup.sh && crond -f"
    profiles:
      - backup

volumes:
  mongodb_data_prod:
    driver: local
  mongodb_config_prod:
    driver: local
  redis_data_prod:
    driver: local
  prometheus_data_prod:
    driver: local
  grafana_data_prod:
    driver: local
  elasticsearch_data_prod:
    driver: local

networks:
  autogitpilot-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
