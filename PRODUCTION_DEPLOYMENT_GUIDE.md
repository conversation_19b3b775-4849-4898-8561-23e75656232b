# Production Deployment Troubleshooting Guide

## Current Issues and Fixes

### 1. **404 Errors on API Endpoints**

**Problem**: Frontend getting 404 errors on `/api/profile/readme/history`, `/api/payment/create-order`

**Root Cause**: CORS configuration not allowing requests from production domain

**Fix Applied**:
- Updated `backend/server.js` to include production domain in CORS origins
- Added logging to track CORS requests

### 2. **500 Error on `/api/auth/sync`**

**Problem**: Authentication sync failing with internal server error

**Root Cause**: Clerk middleware not properly configured for production

**Fix Applied**:
- Enhanced error logging in Clerk middleware
- Added request logging for debugging

### 3. **"User not found" Errors**

**Problem**: `req.auth.userId` not being populated correctly

**Root Cause**: Clerk authentication token not being passed correctly

## Environment Variables Checklist

### Backend (.env.production)
- [ ] `CLERK_SECRET_KEY` - Set to production Clerk secret key
- [ ] `CLERK_PUBLISHABLE_KEY` - Set to production Clerk publishable key
- [ ] `MONGODB_URI` - Set to production MongoDB connection string
- [ ] `FRONTEND_URL` - Set to `https://automaxlib.onrender.com`
- [ ] `ALLOWED_ORIGINS` - Set to `https://automaxlib.onrender.com,https://www.automaxlib.onrender.com`

### Frontend (.env.production)
- [ ] `VITE_CLERK_PUBLISHABLE_KEY` - Set to production Clerk publishable key
- [ ] `VITE_API_BASE_URL` - Set to `https://automaxlib.onrender.com/api`

## Render Deployment Steps

### 1. Backend Deployment
1. Go to Render Dashboard
2. Select your backend service
3. Go to Environment tab
4. Add/Update these environment variables:
   ```
   NODE_ENV=production
   CLERK_SECRET_KEY=sk_live_your_actual_key
   CLERK_PUBLISHABLE_KEY=pk_live_your_actual_key
   MONGODB_URI=your_mongodb_connection_string
   FRONTEND_URL=https://automaxlib.onrender.com
   ALLOWED_ORIGINS=https://automaxlib.onrender.com
   ```
5. Deploy the service

### 2. Frontend Deployment
1. Go to Render Dashboard
2. Select your frontend service
3. Go to Environment tab
4. Add/Update these environment variables:
   ```
   VITE_CLERK_PUBLISHABLE_KEY=pk_live_your_actual_key
   VITE_API_BASE_URL=https://automaxlib.onrender.com/api
   ```
5. Deploy the service

## Testing Steps

### 1. Test CORS
```bash
curl -H "Origin: https://automaxlib.onrender.com" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     https://automaxlib.onrender.com/api/health
```

### 2. Test Authentication
```bash
curl -X GET https://automaxlib.onrender.com/api/user/profile \
     -H "Authorization: Bearer YOUR_CLERK_TOKEN"
```

### 3. Test API Endpoints
```bash
# Test auth sync
curl -X POST https://automaxlib.onrender.com/api/auth/sync \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_CLERK_TOKEN" \
     -d '{"email":"<EMAIL>","firstName":"Test","lastName":"User"}'

# Test profile endpoints
curl -X GET https://automaxlib.onrender.com/api/profile/readme/history \
     -H "Authorization: Bearer YOUR_CLERK_TOKEN"
```

## Common Issues and Solutions

### Issue 1: CORS Errors
**Symptoms**: Browser console shows CORS policy errors
**Solution**: 
- Verify `ALLOWED_ORIGINS` environment variable includes your frontend domain
- Check browser network tab for preflight OPTIONS requests

### Issue 2: Authentication Failures
**Symptoms**: 401 Unauthorized errors, "User not found"
**Solution**:
- Verify Clerk keys are correctly set in production
- Check that frontend is sending Authorization header
- Verify Clerk webhook endpoints are configured

### Issue 3: Database Connection Issues
**Symptoms**: 500 errors, "Database connection failed"
**Solution**:
- Verify MongoDB URI is correct and accessible from Render
- Check MongoDB Atlas network access settings
- Verify database user permissions

## Monitoring and Debugging

### 1. Check Render Logs
```bash
# Backend logs
render logs --service your-backend-service-name

# Frontend logs  
render logs --service your-frontend-service-name
```

### 2. Enable Debug Logging
Add to backend environment:
```
LOG_LEVEL=debug
```

### 3. Health Check Endpoints
- Backend: `https://automaxlib.onrender.com/health`
- API Status: `https://automaxlib.onrender.com/api`

## Next Steps After Deployment

1. **Monitor Error Rates**: Check Render logs for any recurring errors
2. **Test All Features**: Verify authentication, payments, GitHub integration
3. **Performance Testing**: Check response times and optimize if needed
4. **Set Up Monitoring**: Configure alerts for downtime or errors
